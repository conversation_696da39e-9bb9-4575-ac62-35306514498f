<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.cosfo</groupId>
    <artifactId>cosfo-mall</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>cosfo-mall</name>
    <description>cosfo-mall</description>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.3.7.RELEASE</spring-boot.version>
        <jwt.version>0.9.1</jwt.version>
        <qiniu.version>[7.2.0, 7.2.99]</qiniu.version>
        <commons-io.version>1.3.1</commons-io.version>
        <smart-doc.version>2.5.2</smart-doc.version>
        <rocket-mq.version>1.2.0</rocket-mq.version>
        <xianmu-log.version>1.0.14-RELEASE</xianmu-log.version>
        <xianmu-dubbo.version>1.0.14-RELEASE</xianmu-dubbo.version>
        <xianmu-common.version>1.1.15-RELEASE</xianmu-common.version>
        <oms-client.version>1.0.5-RELEASE</oms-client.version>
        <tms-client.version>1.0.6</tms-client.version>
        <org.mapstruct.version>1.3.1.Final</org.mapstruct.version>
        <inventory-client.version>2.0.20-RELEASE</inventory-client.version>
        <ofc-client.version>1.6.8-RELEASE</ofc-client.version>
        <wnc-client.version>1.1.0-RELEASE</wnc-client.version>
        <org.mapstruct.version>1.5.3.Final</org.mapstruct.version>
        <spock.version>2.3-groovy-4.0</spock.version>
        <usercenter-client.version>1.2.7-RELEASE</usercenter-client.version>
        <ordercenter.client.version>1.4.6-RELEASE</ordercenter.client.version>
        <nacos-config.version>0.2.12</nacos-config.version>
        <authentication-sdk.version>1.1.15</authentication-sdk.version>
        <authentication-client.version>1.2.7-RELEASE</authentication-client.version>
        <open-platform-client.version>1.0-RELEASE</open-platform-client.version>
        <marketingcenter-client.version>1.0.9-RELEASE</marketingcenter-client.version>
    </properties>

    <repositories>
        <repository>
            <id>central</id>
            <url>https://maven.aliyun.com/nexus/content/groups/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>snapshots</id>
            <url>https://maven.aliyun.com/nexus/content/groups/public</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>rdc-releases</id>
            <url>https://packages.aliyun.com/maven/repository/2153983-release-efpEBf/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>rdc-snapshots</id>
            <url>https://packages.aliyun.com/maven/repository/2153983-snapshot-NWRePE/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <dependencies>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-mybatis-interceptor-support</artifactId>
            <version>1.0.6-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>cosfo-common</artifactId>
            <version>1.0.4</version>
        </dependency>

        <!-- 国际化sdk依赖 -->
        <dependency>
            <groupId>net.xianmu.i18n</groupId>
            <artifactId>xianmu-i18n-sdk</artifactId>
            <version>1.0.2-RELEASE</version>
        </dependency>


        <!-- auth 服务依赖-->
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>authentication-sdk</artifactId>
            <version>${authentication-sdk.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.elasticsearch</groupId>
                    <artifactId>elasticsearch</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.rocketmq</groupId>
                    <artifactId>rocketmq-spring-boot</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.elasticsearch.client</groupId>
                    <artifactId>elasticsearch-rest-client</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>org.elasticsearch.client</groupId>
                    <artifactId>elasticsearch-rest-high-level-client</artifactId>
                </exclusion>
                <!-- org/mybatis/mybatis/3.5.0 -->
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-spring</artifactId>
            <version>1.7.1</version>
        </dependency>

        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>authentication-client</artifactId>
            <version>${authentication-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>net.summerfarm</groupId>
                    <artifactId>summerfarm-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
            <version>${nacos-config.version}</version>
        </dependency>

        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-launcher</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>junit-platform-engine</artifactId>
                    <groupId>org.junit.platform</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>tms-client</artifactId>
            <version>${tms-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>net.summerfarm</groupId>
                    <artifactId>summerfarm-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>http-clients</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

      <dependency>
        <groupId>net.xianmu</groupId>
        <artifactId>summerfarm-inventory-client</artifactId>
        <version>${inventory-client.version}</version>
      </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-wnc-client</artifactId>
            <version>${wnc-client.version}</version>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>ofc-client</artifactId>
            <version>${ofc-client.version}</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu.common</groupId>
            <artifactId>xianmu-common</artifactId>
            <version>${xianmu-common.version}</version>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>mall-client</artifactId>
            <version>1.0.3-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>net.xianmu.common</groupId>
                    <artifactId>xianmu-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-dubbo-support</artifactId>
            <version>${xianmu-dubbo.version}</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-log-support</artifactId>
            <version>${xianmu-log.version}</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-task-support</artifactId>
            <version>1.0.5</version>
        </dependency>

        <!-- dependencyManagement中配置版本号，在core工程中配置依赖 -->
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-rocketmq-support</artifactId>
            <version>${rocket-mq.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-registry-nacos</artifactId>
            <version>2.7.15</version>
        </dependency>
        <!-- 鲜沐rpc服务-->
        <dependency>
            <groupId>net.manage.client</groupId>
            <artifactId>manage-client</artifactId>
            <version>1.0.28-RELEASE</version>
        </dependency>

        <!--    用户中心-->
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>usercenter-client</artifactId>
            <version>${usercenter-client.version}</version>
        </dependency>
        <!--    订单中心-->
        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>order-center-client</artifactId>
            <version>${ordercenter.client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>oms-client</artifactId>
            <version>${oms-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>cosfo-manage-client</artifactId>
            <version>1.4.1-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>cosfo-mall-client</artifactId>
            <version>1.0.7</version>
        </dependency>
        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>message-client</artifactId>
            <version>1.4.1-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>item-center-client</artifactId>
            <version>1.0.53-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu.common</groupId>
            <artifactId>xianmu-robot-util</artifactId>
            <version>1.0.2</version>
        </dependency>
        <!-- 货品中心 -->
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>goods-center-client</artifactId>
            <version>1.1.3.3-RELEASE</version>
        </dependency>

      <dependency>
        <groupId>net.summerfarm</groupId>
        <artifactId>common-client</artifactId>
        <version>1.0.12-RELEASE</version>
      </dependency>

        <dependency>
            <groupId>net.xianmu.open</groupId>
            <artifactId>open-platform-client</artifactId>
            <version>${open-platform-client.version}</version>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>sf-mall-manage-client</artifactId>
            <version>1.2.8-GEORGE-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-payment-sdk</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>

        <!-- 二方库 -->
        <dependency>
            <groupId>com.cosfo.summerfarm</groupId>
            <artifactId>saas-to-summerfarm</artifactId>
            <version>1.6.12-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>cosfo-store-inventory</artifactId>
            <version>1.0.4-RELEASE</version>
        </dependency>

        <!--    &lt;!&ndash;  springboot 核心依赖包  &ndash;&gt;-->
        <!--    <dependency>-->
        <!--      <groupId>org.springframework.boot</groupId>-->
        <!--      <artifactId>spring-boot-starter-data-jpa</artifactId>-->
        <!--    </dependency>-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>2.2</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>marketing-center-client</artifactId>
            <version>${marketingcenter-client.version}</version>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.lettuce</groupId>
                    <artifactId>lettuce-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis-reactive</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.1</version>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>junit-platform-engine</artifactId>
                    <groupId>org.junit.platform</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>junit-jupiter</artifactId>
                    <groupId>org.junit.jupiter</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.alibaba/druid-spring-boot-starter -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.2.9</version>
        </dependency>
        <!--   分页插件     -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.4.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- https://mvnrepository.com/artifact/cn.hutool/hutool-all -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.22</version>
        </dependency>
        <!-- rocket mq -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <!-- Spring Security和JWT整合 -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-jwt</artifactId>
            <version>1.0.10.RELEASE</version>
        </dependency>
        <!-- JWT -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>${jwt.version}</version>
        </dependency>
        <!-- 字符串转换需要用到此包 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.4</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.3.6</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.3.6</version>
        </dependency>

        <!--  emoji  -->
        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>emoji-java</artifactId>
            <version>4.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>

        <!--解析xml工具xstream-->
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.4</version>
        </dependency>
        <!--  七牛上传SDK  -->
        <dependency>
            <groupId>com.qiniu</groupId>
            <artifactId>qiniu-java-sdk</artifactId>
            <version>${qiniu.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons-io.version}</version>
        </dependency>

        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.4</version>
        </dependency>

        <!-- SchedulerX 分布式调度平台 -->
        <!--    <dependency>-->
        <!--      <groupId>com.aliyun.schedulerx</groupId>-->
        <!--      <artifactId>schedulerx2-spring-boot-starter</artifactId>-->
        <!--      <version>1.4.0</version>-->
        <!--      &lt;!&ndash;如果用的是logback，需要把log4j和log4j2排除掉 &ndash;&gt;-->
        <!--      <exclusions>-->
        <!--        <exclusion>-->
        <!--          <groupId>org.apache.logging.log4j</groupId>-->
        <!--          <artifactId>log4j-api</artifactId>-->
        <!--        </exclusion>-->
        <!--        <exclusion>-->
        <!--          <groupId>org.apache.logging.log4j</groupId>-->
        <!--          <artifactId>log4j-core</artifactId>-->
        <!--        </exclusion>-->
        <!--        <exclusion>-->
        <!--          <groupId>log4j</groupId>-->
        <!--          <artifactId>log4j</artifactId>-->
        <!--        </exclusion>-->
        <!--      </exclusions>-->
        <!--    </dependency>-->

        <!-- redisson -->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.16.4</version>
        </dependency>

        <!--https://gitee.com/sunyurepository/ApplicationPower-->
        <dependency>
            <groupId>com.github.shalousun</groupId>
            <artifactId>smart-doc</artifactId>
            <version>${smart-doc.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.7.0</version>
        </dependency>


        <dependency>
            <groupId>com.github.shalousun</groupId>
            <artifactId>smart-doc-maven-plugin</artifactId>
            <version>${smart-doc.version}</version>
        </dependency>

        <!-- mapstruct -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>

        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>junit-platform-engine</artifactId>
                    <groupId>org.junit.platform</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
            <scope>test</scope>
        </dependency>

        <!--    <dependency>-->
        <!--      <groupId>org.junit.platform</groupId>-->
        <!--      <artifactId>junit-platform-engine</artifactId>-->
        <!--      <version>1.9.0</version>-->
        <!--      <scope>test</scope>-->
        <!--    </dependency>-->

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.9.2</version>
            <scope>test</scope>
        </dependency>


    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.spockframework</groupId>
                <artifactId>spock-bom</artifactId>
                <version>2.3-groovy-4.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.16</version>
                        </path>
                        <!-- This is needed when using Lombok 1.18.16 and above -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <!-- other annotation processors -->
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.7.RELEASE</version>
                <configuration>
                    <mainClass>com.cosfo.mall.CosfoMallApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.github.shalousun</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>${smart-doc.version}</version>
                <configuration>
                    <includes>
                        <!--格式为：groupId:artifactId;参考如下-->
                        <!--也可以支持正则式如：com.alibaba:.* -->
                        <include>com.cosfo.mall.*.controller:.*</include>
                        <include>com.github.shalousun:.*</include>
                    </includes>
                    <!--指定生成文档的使用的配置文件-->
                    <configFile>./src/main/resources/smart-doc.json</configFile>
                    <!--指定项目名称-->
                    <projectName>测试</projectName>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>html</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>