package com.cosfo.mall.payment.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * payment_item
 * <AUTHOR>
@Data
public class PaymentItem implements Serializable {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 支付单id
     */
    private Long paymentId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单金额
     */
    private BigDecimal orderPrice;

    /**
     * 支付类型
     */
    private Integer payType;

    /**
     * 线下 支付凭证
     */
    private String paymentReceipt;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}