package com.cosfo.mall.payment.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.math.Money;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.server.HttpServerRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.mall.bill.mapper.BillProfitSharingMapper;
import com.cosfo.mall.bill.model.dto.BillProfitSharingOrderDTO;
import com.cosfo.mall.bill.model.po.BillProfitSharing;
import com.cosfo.mall.bill.service.BillProfitSharingOrderService;
import com.cosfo.mall.bill.service.BillProfitSharingService;
import com.cosfo.mall.common.config.DingTalkRobotConfig;
import com.cosfo.mall.common.config.FanTaiPaymentConfig;
import com.cosfo.mall.common.config.HuiFuConfig;
import com.cosfo.mall.common.constant.Constants;
import com.cosfo.mall.common.constant.MQTopicConstant;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constant.WechatPaymentConstant;
import com.cosfo.mall.common.constants.*;
import com.cosfo.mall.common.context.BillProfitSharingOrderStatusEnum;
import com.cosfo.mall.common.context.PaymentEnum;
import com.cosfo.mall.common.context.RefundEnum;
import com.cosfo.mall.common.context.shard.AccountTypeEnum;
import com.cosfo.mall.common.factory.PayStrategyFactory;
import com.cosfo.mall.common.result.ResultDTO;
import com.cosfo.mall.common.utils.*;
import com.cosfo.mall.facade.dto.payment.PaymentChannelQueryByIdDTO;
import com.cosfo.mall.facade.payment.PaymentChannelFacade;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.order.mapper.HuiFuPaymentMapper;
import com.cosfo.mall.order.model.dto.AcctInfoDTO;
import com.cosfo.mall.order.model.dto.HuiFuConfirmResponseDTO;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.order.model.po.HuiFuiPaymentReceive;
import com.cosfo.mall.order.service.OrderService;
import com.cosfo.mall.payment.mapper.PaymentCombinedDetailMapper;
import com.cosfo.mall.payment.mapper.PaymentItemMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.mapper.RefundMapper;
import com.cosfo.mall.payment.model.dto.OrderPayResultDTO;
import com.cosfo.mall.payment.model.dto.PayNotifyMessageDTO;
import com.cosfo.mall.payment.model.dto.PaymentDTO;
import com.cosfo.mall.payment.model.po.*;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.payment.service.*;
import com.cosfo.mall.payment.template.PayTemplate;
import com.cosfo.mall.payment.utils.OrderPaymentHelper;
import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import com.cosfo.mall.tenant.service.TenantAuthConnectionService;
import com.cosfo.mall.tenant.service.TenantService;
import com.cosfo.mall.wechat.api.HuiFuApi;
import com.cosfo.mall.wechat.api.PayMchAPI;
import com.cosfo.mall.wechat.bean.constant.WechatNotifyEventType;
import com.cosfo.mall.wechat.bean.constant.WechatPayTradeResult;
import com.cosfo.mall.wechat.bean.huifu.QryAliConfDTO;
import com.cosfo.mall.wechat.bean.huifu.QryWxConfDTO;
import com.cosfo.mall.wechat.bean.huifu.SettleMentInfoDTO;
import com.cosfo.mall.wechat.bean.notify.DirectNotify;
import com.cosfo.mall.wechat.bean.notify.NotifyResponse;
import com.cosfo.mall.wechat.bean.notify.WxResponse;
import com.cosfo.mall.wechat.bean.paymch.DirectQueryResult;
import com.cosfo.mall.wechat.bean.profitsharing.*;
import com.cosfo.mall.wechat.bean.refund.RefundNotify;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderCommandProvider;
import com.cosfo.ordercenter.client.req.ProfitSharingFinishTimeReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.payment.routing.common.enums.PaymentBusinessLineEnums;
import net.summerfarm.payment.routing.common.enums.PaymentChannelProviderEnums;
import net.summerfarm.payment.trade.adapter.dinpay.dto.notify.DinPayNotifyDTO;
import net.summerfarm.payment.trade.adapter.dinpay.dto.notify.PaymentAttachInfoDTO;
// 智付分账相关导入
import net.summerfarm.payment.trade.model.request.SplitRule;
import net.summerfarm.payment.trade.model.request.UnifiedProfitSharingRequest;
import net.summerfarm.payment.trade.common.enums.ProfitSharingStatus;
import net.summerfarm.payment.trade.model.request.UnifiedQueryProfitSharingRequest;
import net.summerfarm.payment.trade.model.config.ChannelConfig;
import net.summerfarm.payment.trade.model.response.UnifiedProfitSharingResult;
import net.summerfarm.payment.trade.model.response.UnifiedQueryProfitSharingResult;
import net.summerfarm.payment.trade.service.PaymentClientService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * <AUTHOR>
 * @date 2022/5/23  11:25
 */
@Slf4j
@Service
public class PaymentServiceImpl implements PaymentService {
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private TenantService tenantService;
    @Lazy
    @Resource
    private OrderService orderService;
    @Resource
    private RefundService refundService;
    @Resource
    private RefundMapper refundMapper;
    @Value("${notify-domain}")
    private String notifyDomain;
    @Value("${wechat-api-key}")
    private String wechatApiSecret;
    @Resource
    private FanTaiPaymentConfig fanTaiPaymentConfig;
    @Resource
    private BillProfitSharingService billProfitSharingService;
    @Resource
    private BillProfitSharingOrderService billProfitSharingOrderService;
    @Resource
    private BillProfitSharingMapper billProfitSharingMapper;
    @Resource
    private HuiFuPaymentMapper huiFuPaymentMapper;
    @Resource
    private HuifuPaymentService huifuPaymentService;
    @Resource(name = "orderNotifyExecutorService")
    private ExecutorService orderNotifyExecutorService;
    @Resource
    private HuiFuPaymentRateRetryService huiFuPaymentRateRetryService;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Lazy
    @Autowired
    private PaymentService paymentService;

    @DubboReference
    private OrderCommandProvider orderCommandProvider;

    /**
     * 汇付配置信息
     */
    @Resource
    private HuiFuConfig huiFuConfig;
    @Resource
    private TenantAuthConnectionService tenantAuthConnectionService;
    @Resource
    private DingTalkRobotConfig dingTalkRobotConfig;
    @Lazy
    @Resource
    private PayStrategyFactory payStrategyFactory;
    @Resource
    private OrderPaymentHelper paymentOrderHelper;
    @Resource
    private PlatformTransactionManager transactionManager;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private PaymentCombinedDetailMapper paymentCombinedDetailMapper;
    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;
    @Resource
    private PaymentChannelFacade paymentChannelFacade;
    @Resource
    private PaymentClientService paymentClientService;


    @Override
    public PaymentResult pay(PaymentRequest paymentRequest) {
        log.info("支付参数：[{}]", JSON.toJSONString(paymentRequest));

        // 获取支付渠道
        getOnlinePaymentChannel(paymentRequest);
        Pair<String, String> payCodePair = PayCodeEnum.getPayCode(paymentRequest.getPayType(), paymentRequest.getOnlinePayChannel(), paymentRequest.getH5Request(), paymentRequest.getPayMark());
        String payCode = payCodePair.getKey();
        String tradeType = payCodePair.getValue();
        paymentRequest.setTradeType(tradeType);

        return payStrategyFactory.getPayChannel(payCode).pay(paymentRequest);
    }

    /**
     * 获取支付渠道
     * mini-app 微信直连、汇付微信间连、汇付支付宝间连
     * H5 汇付微信间连、汇付支付宝间连
     *
     * @param paymentRequest
     */
    private void getOnlinePaymentChannel(PaymentRequest paymentRequest) {
        Integer payType = paymentRequest.getPayType();
        if (Objects.equals(paymentRequest.getPayType(), PayTypeEnum.COMBINED_PAY.getType())) {
            List<Integer> payTypes = paymentRequest.getPayTypes();
            if (CollectionUtils.isEmpty(payTypes) || payTypes.size() != 2) {
                throw new ProviderException("组合支付参数错误");
            }
            payType = payTypes.stream().filter(PayTypeEnum::isCurrentPay).findFirst().orElse(payType);
        }

        // 不是微信或者支付宝直接return
        if (!Objects.equals(payType, PayTypeEnum.WECHAT_PAY.getType()) &&
                !Objects.equals(payType, PayTypeEnum.ALI_PAY.getType())) {
            return;
        }

        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        TenantAuthConnectionDTO tenantAuthConnectionDTO = tenantAuthConnectionService.selectByTenantId(loginContextInfoDTO.getTenantId());
        Integer indirectOnlineChannel = tenantAuthConnectionDTO.getIndirectOnlineChannel();

        // 1. 如果是公众号
        if (paymentRequest.getH5Request()) {
            // 公众号都是间连，选取间连渠道 汇付 or 智付即可
            paymentRequest.setOnlinePayChannel(indirectOnlineChannel);
            return;
        }

        // 小程序不支持支付宝
        if (Objects.equals(payType, PayTypeEnum.ALI_PAY.getType())) {
            throw new BizException("小程序暂不支持支付宝支付，请选择公众号或者浏览器");
        }

        // 2.微信分直连、间连、汇付提供的间连插件
        Integer wechatDirectSwitch = tenantAuthConnectionDTO.getWechatDirectSwitch();
        Integer wechatIndirectSwitch = tenantAuthConnectionDTO.getWechatIndirectSwitch();
        Integer wechatIndirectPluginSwitch = tenantAuthConnectionDTO.getWechatIndirectPluginSwitch();

        if (PaymentEnum.Switch.OPEN.getType().equals(wechatDirectSwitch)) {
            paymentRequest.setOnlinePayChannel(OnlinePayChannelEnum.WECHAT_PAY.getChannel());
            return;
        }
        if (PaymentEnum.Switch.OPEN.getType().equals(wechatIndirectSwitch)) {
            paymentRequest.setOnlinePayChannel(indirectOnlineChannel);
            return;
        }
        if (PaymentEnum.Switch.OPEN.getType().equals(wechatIndirectPluginSwitch)) {
            paymentRequest.setOnlinePayChannel(OnlinePayChannelEnum.HUIFU_PAY.getChannel());
            paymentRequest.setPayMark(PayTypeFlagEnum.APPLET_HUI_FU_PLUGIN_FLAG.getCode());
            return;
        }
        throw new BizException("暂无可用的支付方式");
    }

    @Override
    public String queryByPartyOrderId(String partyOrderId) {
        if (StringUtils.isEmpty(partyOrderId)) {
            throw new ParamsException("商户订单号不能为空");
        }
        LambdaQueryWrapper<HuiFuPayment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HuiFuPayment::getPartyOrderId, partyOrderId);
        HuiFuPayment huiFuPayment = huiFuPaymentMapper.selectOne(queryWrapper);
        if (huiFuPayment == null) {
            return null;
        }
        return huiFuPayment.getTransAmt();
    }

    @Override
    public DirectQueryResult queryPayResult(Long paymentId) {
        Payment payment = paymentMapper.selectByPrimaryKey(paymentId);
        if (payment == null) {
            throw new BizException("支付信息不存在");
        }
        TenantAuthConnectionDTO authConnectionDTO = tenantAuthConnectionService.queryHistoryPayConfig(payment.getTenantId(), null, payment.getSpMchid());
        DirectQueryResult queryResult = PayMchAPI.queryDirectPayResult(payment.getSpMchid(), payment.getPaymentNo(), authConnectionDTO.getPayCertPath());
        log.info("支付查询结果：{}", JSONObject.toJSONString(queryResult));

        return queryResult;
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public NotifyResponse wxDirectPayNotify(DirectNotify directNotify, HttpServerRequest request) {
        log.info("收到微信回调数据：{}", JSONObject.toJSONString(directNotify));
        AesUtil aesUtil = new AesUtil(wechatApiSecret.getBytes(StandardCharsets.UTF_8));

        try {
            String decryptStr = aesUtil.decryptToString(directNotify.getResource().getAssociatedData().getBytes(StandardCharsets.UTF_8), directNotify.getResource().getNonce().getBytes(StandardCharsets.UTF_8), directNotify.getResource().getCiphertext());
            log.info("收到微信回调数据：{}", decryptStr);
            String payCertPath = null;
            switch (directNotify.getEventType()) {
                case WechatNotifyEventType.PAY_SUCCESS:
                    DirectQueryResult result = JSONObject.parseObject(decryptStr, DirectQueryResult.class);
                    //校验签名
                    if (result.getMchid().equals(fanTaiPaymentConfig.getMchId())) {
                        payCertPath = fanTaiPaymentConfig.getPayCertPath();
                    } else {
                        TenantAuthConnectionDTO dto = tenantService.queryByAppId(result.getAppid());
                        payCertPath = dto.getPayCertPath();
                    }

                    if (!checkNotifySign(request, payCertPath)) {
                        return NotifyResponse.fail();
                    }
                    String notifyInfo = JSON.toJSONString(result);
                    log.info("微信回调信息:{}", notifyInfo);
                    handleWXPayNotify(result);
                    break;
                case WechatNotifyEventType.REFUND_SUCCESS:
                case WechatNotifyEventType.REFUND_ABNORMAL:
                case WechatNotifyEventType.REFUND_CLOSED:
                    RefundNotify refundNotify = JSONObject.parseObject(decryptStr, RefundNotify.class);

                    //校验签名
                    Refund refund = refundMapper.selectByRefundNo(refundNotify.getOutRefundNo());
                    // 查询原支付订单
                    // 获取证书
                    //校验签名
                    if (refund.getSubMchid().equals(fanTaiPaymentConfig.getMchId())) {
                        payCertPath = fanTaiPaymentConfig.getPayCertPath();
                    } else {
                        TenantAuthConnectionDTO authConnectionDTO = tenantService.queryTenantAuthConnection(refund.getTenantId());
                        payCertPath = authConnectionDTO.getPayCertPath();
                    }

                    if (!checkNotifySign(request, payCertPath)) {
                        return NotifyResponse.fail();
                    }

                    refundService.handleWxRefundNotify(refundNotify);
                    break;
                default:
                    return NotifyResponse.fail();
            }
        } catch (Exception e) {
            log.error("回调消息处理异常，e={}", e.getMessage(), e);
            return NotifyResponse.fail();
        }

        return NotifyResponse.success();
    }

    /**
     * 校验回调签名信息
     *
     * @param request     回调
     * @param certPayPath 证书路径
     * @return 校验是否通过
     */
    private boolean checkNotifySign(HttpServerRequest request, String certPayPath) {
//        String timestamp = request.getHeader("Wechatpay-Timestamp", StandardCharsets.UTF_8);
//        String nonceStr = request.getHeader("Wechatpay-Nonce", StandardCharsets.UTF_8);
//        String certSerialNo = request.getHeader("Wechatpay-Serial", StandardCharsets.UTF_8);
//        String signature = request.getHeader("Wechatpay-Signature", StandardCharsets.UTF_8);

        //todo 签名验证、证书更新逻辑

        return true;
    }

    /**
     * 处理支付成功信息
     *
     * @param result 支付回调
     */
    @Transactional
    @Override
    public boolean handleWXPayNotifyTransaction(DirectQueryResult result) {
        log.info("处理微信支付回调信息，paymentNo：{}，result：{}", result.getOutTradeNo(), result);
        // 锁定所有的支付单
        List<Payment> payments = lockOrderAllPayments(result.getOutTradeNo());
        // 本次回调支付单
        Payment payment = payments.stream().filter(el -> Objects.equals(el.getPaymentNo(), result.getOutTradeNo())).findFirst().orElseThrow(() -> new ProviderException("未查询到支付单信息"));
        // 处理支付幂等
        boolean noCallBackHasProcessed = noCallBackHasProcessed(payment, payments);
        if (!noCallBackHasProcessed) {
            return false;
        }

        //更新支付单信息
        Payment updatePayment = new Payment();
        updatePayment.setId(payment.getId());
        updatePayment.setTransactionId(result.getTransactionId());
        if (!Objects.equals(payment.getTradeType(), TradeTypeEnum.COMBINED_PAY.getDesc())) {
            updatePayment.setTradeType(result.getTradeType());
        }
        updatePayment.setTradeState(result.getTradeState());
        updatePayment.setTradeStateDesc(result.getTradeStateDesc());
        updatePayment.setBankType(result.getBankType());
        updatePayment.setSuccessTime(result.getSuccessTime());
        updatePayment.setFinishTime(LocalDateTime.now());
        if (Objects.nonNull(result.getPayer())) {
            updatePayment.setSpOpenid(result.getPayer().getOpenid());
        }
        if (WechatPayTradeResult.SUCCESS.equals(result.getTradeState())) {
            updatePayment.setStatus(PaymentEnum.Status.SUCCESS.getCode());
            updatePayment.setFeeRate(WechatPaymentConstant.WX_TAX_RATE);
        } else if (WechatPayTradeResult.CLOSED.equals(result.getTradeState())) {
            updatePayment.setStatus(PaymentEnum.Status.CANCELED.getCode());
        }
        paymentMapper.updateByPrimaryKeySelective(updatePayment);

        if (Objects.equals(payment.getTradeType(), TradeTypeEnum.COMBINED_PAY.getDesc())) {
            List<PaymentCombinedDetail> details = paymentCombinedDetailService.selectByCombinedPaymentNo(result.getOutTradeNo());
            PaymentCombinedDetail wechatCombinedDetail = details.stream()
                    .filter(el -> Objects.equals(el.getOnlinePayChannel(), OnlinePayChannelEnum.WECHAT_PAY.getChannel()))
                    .findFirst()
                    .orElseThrow(() -> new ProviderException("未查询到微信支付子单"));
            PaymentCombinedDetail update = getWxCombinedDetail(result, wechatCombinedDetail, updatePayment);
            paymentCombinedDetailService.updateByPrimaryKeySelective(update);

            PaymentCombinedDetail nativeCombinedDetail = details.stream()
                    .filter(el -> Objects.isNull(el.getOnlinePayChannel()))
                    .findFirst()
                    .orElseThrow(() -> new ProviderException("未查询到本地支付子单"));
            paymentCombinedDetailService.decreaseFreezeAmount(nativeCombinedDetail);
        }
        log.info("处理微信支付回调信息完成，paymentNo：{}", result);
        return true;
    }

    private static PaymentCombinedDetail getWxCombinedDetail(DirectQueryResult result, PaymentCombinedDetail combinedDetail, Payment updatePayment) {
        PaymentCombinedDetail update = new PaymentCombinedDetail();
        update.setId(combinedDetail.getId());
        update.setTransactionId(updatePayment.getTransactionId());
        update.setTradeType(result.getTradeType());
        update.setTradeState(updatePayment.getTradeState());
        update.setTradeStateDesc(updatePayment.getTradeStateDesc());
        update.setBankType(updatePayment.getBankType());
        update.setSuccessTime(updatePayment.getSuccessTime());
        update.setFinishTime(updatePayment.getFinishTime());
        update.setSpOpenid(updatePayment.getSpOpenid());
        update.setStatus(updatePayment.getStatus());
        update.setFeeRate(updatePayment.getFeeRate());
        return update;
    }

    @Override
    public void handleWXPayNotify(DirectQueryResult result) {
        boolean transactionResult = paymentService.handleWXPayNotifyTransaction(result);
        // 发送支付结果通知
        if (transactionResult) {
            sendPayNotifyMessage(result.getOutTradeNo());
        }
    }

    @Override
    public ResultDTO<ProfitSharingOrderResult> handleProfitSharingOrderResult(QueryOrderParams queryOrderParams, List<BillProfitSharing> billProfitSharingList) {
        ProfitSharingOrderResult profitSharingOrderResult = PayMchAPI.queryProfitsharingOrder(queryOrderParams, fanTaiPaymentConfig.getPayCertPath(), fanTaiPaymentConfig.getMchId());
        transactionTemplate.execute(status -> {
            Boolean result = true;
            try {
                handleProfitSharingResult(profitSharingOrderResult, billProfitSharingList);
            } catch (Exception e) {
                log.error("{}订单查询分账结果失败,{}", queryOrderParams.getOrderId(), e.getMessage(), e);
                status.setRollbackOnly();
                result = false;
            }
            return result;
        });
        return ResultDTO.success(profitSharingOrderResult);
    }

    @Override
    public ResultDTO<HuiFuConfirmResponseDTO> handleHuiFuProfitSharingOrderResult(QueryOrderParams queryOrderParams, List<BillProfitSharing> billProfitSharingList, BillProfitSharingOrderDTO billProfitSharingOrderDTO) {
        HuiFuConfirmResponseDTO huiFuConfirmResponseDTO = PayMchAPI.queryHuiFuProfitsharingOrder(queryOrderParams, huiFuConfig);
        transactionTemplate.execute(status -> {
            Boolean result = true;
            try {
                handleHuiFuProfitSharingResult(billProfitSharingList, huiFuConfirmResponseDTO, billProfitSharingOrderDTO);
            } catch (Exception e) {
                log.error("{}订单查询分账结果失败", queryOrderParams.getOrderId());
                status.setRollbackOnly();
                result = false;
            }
            return result;
        });
        return ResultDTO.success(huiFuConfirmResponseDTO);
    }

    private void handleProfitSharingResult(ProfitSharingOrderResult profitSharingOrderResult, List<BillProfitSharing> billProfitSharingList) {
        // 分账接收方
        List<ReceiverResult> receivers = profitSharingOrderResult.getReceivers();
        BillProfitSharing fantaiBillProfitSharing = new BillProfitSharing();
        // 遍历分账记录
        for (BillProfitSharing billProfitSharing : billProfitSharingList) {
            // 分账给帆台的，记录下来
            if (fanTaiPaymentConfig.getMchId().equals(billProfitSharing.getAccount())) {
                fantaiBillProfitSharing = billProfitSharing;
            }
            // 给当前分账记录设置微信订单号
            billProfitSharing.setWxOrderId(profitSharingOrderResult.getOrderId());
            // 遍历分账接收方
            for (ReceiverResult result : receivers) {
                // 接收方账号与分账记录的账户相匹配
                if (result.getType().equals(billProfitSharing.getType()) && result.getAccount().equals(billProfitSharing.getAccount())) {
                    // 分账结果，更新分账记录详情
                    billProfitSharing.setDetailId(result.getDetail_id());
                    billProfitSharing.setFailReason(result.getFailReason());
                    // 如果分账接收方状态为等待中
                    if (!ProfitSharingResultEnum.WAITING.getWxCode().equals(result.getResult())) {
                        // 设置分账成功时间为完成时间的八小时后
                        billProfitSharing.setSuccessTime(TimeUtils.changeString2Date(result.getFinish_time(), TimeUtils.FORMAT_BEIJING));
                    }
                    // 返回分账状态
                    ProfitSharingResultEnum profitSharingResultEnum = ProfitSharingResultEnum.getByWxCode(result.getResult());
                    // 分账方更新分账状态
                    billProfitSharing.setStatus(profitSharingResultEnum.getStatus());
                    billProfitSharingService.updateByPrimaryKeySelective(billProfitSharing);
                }
            }
        }

        // 分账结果
        // 非帆台分账记录是否都是成功或失败
        boolean allMatch = billProfitSharingList.stream().filter(billProfitSharing -> !fanTaiPaymentConfig.getMchId().equals(billProfitSharing.getAccount())).allMatch(billProfitSharing -> ProfitSharingResultEnum.FINISHED.getStatus().equals(billProfitSharing.getStatus()) || ProfitSharingResultEnum.FAILED.getStatus().equals(billProfitSharing.getStatus()));
        fantaiBillProfitSharing.setDetailId(receivers.get(NumberConstant.ZERO).getDetail_id());
        fantaiBillProfitSharing.setFailReason(receivers.get(NumberConstant.ZERO).getFailReason());
        // 除帆台以外分账都成功了
        if (allMatch) {
            // 帆台分账记录状态更新为完成
            fantaiBillProfitSharing.setSuccessTime(TimeUtils.changeString2Date(receivers.get(NumberConstant.ZERO).getFinish_time(), TimeUtils.FORMAT_BEIJING));
            fantaiBillProfitSharing.setStatus(ProfitSharingResultEnum.FINISHED.getStatus());
        }
        // 更新分账记录状态和分账账单状态
        billProfitSharingService.updateByPrimaryKeySelective(fantaiBillProfitSharing);
        BillProfitSharing billProfitSharing = billProfitSharingList.get(NumberConstant.ZERO);
        billProfitSharingOrderService.updateBillProfitSharingOrderStatus(billProfitSharing.getTenantId(), billProfitSharing.getOrderId(), allMatch ? BillProfitSharingOrderStatusEnum.FINISHED.getStatus() : BillProfitSharingOrderStatusEnum.PROCESSING.getStatus(), null);
    }

    private void handleHuiFuProfitSharingResult(List<BillProfitSharing> billProfitSharingList, HuiFuConfirmResponseDTO huiFuConfirmResponseDTO, BillProfitSharingOrderDTO billProfitSharingOrderDTO) {
        if (CollectionUtils.isEmpty(billProfitSharingList)) {
            log.warn("没有需要处理的分账记录, response={}", huiFuConfirmResponseDTO);
            return;
        }
        BillProfitSharing represent = billProfitSharingList.get(0);
        // 分账接收方
        BillProfitSharing update = new BillProfitSharing();
        update.setWxOrderId(huiFuConfirmResponseDTO.getHf_seq_id());
        update.setDetailId(huiFuConfirmResponseDTO.getResp_code());
        update.setFailReason(huiFuConfirmResponseDTO.getResp_desc());
        // 返回分账状态
        ProfitSharingResultEnum profitSharingResultEnum = ProfitSharingResultEnum.getByCode(huiFuConfirmResponseDTO.getTrans_stat());
        switch (Objects.requireNonNull(profitSharingResultEnum)) {
            case FAILED:
                update.setStatus(profitSharingResultEnum.getStatus());
                String errMsg = String.format("订单:%s汇付分账失败，失败原因:%s，执行次数:%d次", represent.getOrderId(), huiFuConfirmResponseDTO.getResp_desc(), billProfitSharingOrderDTO.getRetryNum() + 1);
                Long id = billProfitSharingOrderDTO.getId();
                int result = billProfitSharingOrderService.resetStatusAndIncRetryNum(id);
                if (result < 1) {
                    throw new ProviderException("重置分账状态及自增次数失败");
                }
                throw new BizException(errMsg);
            case WAITING:
                update.setStatus(profitSharingResultEnum.getStatus());
                break;
            case PROCESSING:
                break;
            case FINISHED_DETAIL:
                // 处理分账明细
                billProfitSharingOrderService.updateStatusById(billProfitSharingOrderDTO.getId(), BillProfitSharingOrderStatusEnum.PART_FINISHED.getStatus(), BillProfitSharingOrderStatusEnum.PROCESSING.getStatus());
                handlerDetailProfitSharingResult(billProfitSharingList, huiFuConfirmResponseDTO);
                //明细单独处理，不走下面批量更新
                return;
            case FINISHED:
                update.setStatus(profitSharingResultEnum.getStatus());
                update.setSuccessTime(new Date());
                billProfitSharingOrderService.updateStatusById(billProfitSharingOrderDTO.getId(), BillProfitSharingOrderStatusEnum.FINISHED.getStatus(), BillProfitSharingOrderStatusEnum.PROCESSING.getStatus());
                updateProfitSharingFinishTime(represent.getOrderId(), update.getSuccessTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                break;
            default:
        }
        billProfitSharingService.batchUpdateBillProFitSharing(update, billProfitSharingList.stream().map(BillProfitSharing::getId).collect(Collectors.toList()));
        profitSharingResultNotify(represent.getOrderId(), profitSharingResultEnum, billProfitSharingList.size(), billProfitSharingList.size(), profitSharingResultEnum.getDesc());
    }

    private void updateProfitSharingFinishTime(Long orderId, LocalDateTime finishTime) {
        ProfitSharingFinishTimeReq req = new ProfitSharingFinishTimeReq();
        ProfitSharingFinishTimeReq.ProfitSharingFinishTimeOrder finishTimeOrder = new ProfitSharingFinishTimeReq.ProfitSharingFinishTimeOrder();
        finishTimeOrder.setOrderId(orderId);
        finishTimeOrder.setProfitSharingFinishTime(finishTime);
        req.setProfitSharingFinishTimeOrderList(Lists.newArrayList(finishTimeOrder));
        RpcResultUtil.handle(orderCommandProvider.batchUpdateProfitSharingFinishTime(req));
    }


    /**
     * 发送分账结果通知
     *
     * @param orderId
     * @param resultEnum
     */
    @Override
    public void profitSharingResultNotify(Long orderId, ProfitSharingResultEnum resultEnum, Integer total, Integer successCnt, String message) {
        StringBuffer title = new StringBuffer();
        title.append("订单分账结果同步");
        StringBuffer text = new StringBuffer();
        text.append("> 订单号: " + orderId + " \n\n");
        text.append("> 分账结果: " + resultEnum.getDesc() + " \n\n ");
        text.append("> 分账明细总数：" + total + " \n\n");
        text.append("> 分账明细成功数量：" + successCnt + " \n\n");
        text.append("> 分账详细信息：" + message + " \n\n");
        text.append("订单分账结果同步");
        HashMap<String, String> msgMap = new HashMap<>(NumberConstant.TWO);
        msgMap.put(Constants.TITLE, title.toString());
        msgMap.put(Constants.TEXT, text.toString());
        long timestamp = System.currentTimeMillis();
        try {
            String sign = DingTalkRobotUtil.sign(timestamp, dingTalkRobotConfig.getSecret());
            DingTalkRobotUtil.sendMarkDownMsg(dingTalkRobotConfig.getUrl() + "&timestamp=" + timestamp + "&sign=" + sign, () -> msgMap, null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void handlerDetailProfitSharingResult(List<BillProfitSharing> billProfitSharingList, HuiFuConfirmResponseDTO huiFuConfirmResponseDTO) {
        String acctSplitBunch = huiFuConfirmResponseDTO.getAcct_split_bunch();
        int successCnt = 0;
        List<AcctInfoDTO> detailResult = new ArrayList<>();
        if (!StringUtils.isBlank(acctSplitBunch)) {
            detailResult.addAll(JSON.parseArray(acctSplitBunch, AcctInfoDTO.class));
        }
        Map<String, AcctInfoDTO> accInfoMap = detailResult.stream().collect(Collectors.toMap(AcctInfoDTO::getHuifu_id, dto -> dto));
        for (BillProfitSharing billProfitSharing : billProfitSharingList) {
            AcctInfoDTO acctInfoDTO = accInfoMap.get(billProfitSharing.getAccount());
            if (acctInfoDTO == null) {
                continue;
            }
            BillProfitSharing update = new BillProfitSharing();
            ProfitSharingResultEnum profitSharingResultEnum = ProfitSharingResultEnum.getByCode(acctInfoDTO.getTrans_stat());
            if (ProfitSharingResultEnum.FINISHED.equals(profitSharingResultEnum)) {
                update.setSuccessTime(new Date());
                successCnt++;
            }
            update.setId(billProfitSharing.getId());
            update.setStatus(profitSharingResultEnum.getStatus());
            billProfitSharingService.updateByPrimaryKeySelective(update);
        }
        BillProfitSharing billProfitSharing = billProfitSharingList.get(0);
        profitSharingResultNotify(billProfitSharing.getOrderId(), ProfitSharingResultEnum.FINISHED_DETAIL, billProfitSharingList.size(), successCnt, ProfitSharingResultEnum.FINISHED_DETAIL.getDesc());
    }

    @Override
    public PaymentDTO querySuccessPaymentInfoByOrderId(Long orderId, Long tenantId) {
        PaymentItem paymentItem = paymentItemMapper.selectPaySuccessByOrderId(tenantId, orderId);
        if (Objects.isNull(paymentItem)) {
            return null;
        }

        Payment payment = paymentMapper.selectByPrimaryKey(paymentItem.getPaymentId());
        PaymentDTO paymentDTO = new PaymentDTO();
        BeanUtils.copyProperties(payment, paymentDTO);
        List<PaymentItem> paymentItems = paymentItemMapper.selectByPaymentId(payment.getId());
        paymentDTO.setPaymentItemList(paymentItems);
        return paymentDTO;
    }

    @Override
    public void huifuProfitSharing(List<BillProfitSharing> billProfitSharings, HuiFuPayment huiFuPayment, BillProfitSharingOrderDTO billProfitSharingOrderDTO) {
        ProfitSharingOrder profitSharingOrder = castProfitSharingInfoHuiFuByBillProfitSharing(billProfitSharings, false);
        HuiFuConfirmResponseDTO huiFuConfirmResponseDTO = PayMchAPI.profitsharingOrders(profitSharingOrder, huiFuPayment, huiFuConfig);
        handleHuiFuProfitSharingResult(billProfitSharings, huiFuConfirmResponseDTO, billProfitSharingOrderDTO);
    }

    @Override
    public void dinPayProfitSharing(List<BillProfitSharing> billProfitSharings, PaymentDTO paymentDTO, BillProfitSharingOrderDTO billProfitSharingOrderDTO) {
        try {
            log.info("开始智付分账处理 - 分账单号：{}，订单ID：{}", billProfitSharingOrderDTO.getProfitSharingNo(), billProfitSharingOrderDTO.getOrderId());

            // 1. 构建智付分账请求
            UnifiedProfitSharingRequest request = buildDinPayProfitSharingRequest(billProfitSharings, paymentDTO, billProfitSharingOrderDTO);

            // 2. 调用智付分账接口
            UnifiedProfitSharingResult result = paymentClientService.profitSharing(request);

            // 3. 处理分账结果
            handleDinPayProfitSharingResult(billProfitSharings, result, billProfitSharingOrderDTO);

            log.info("智付分账处理完成 - 分账单号：{}，结果：{}", billProfitSharingOrderDTO.getProfitSharingNo(), result.getSuccess());

        } catch (Exception e) {
            log.error("智付分账失败，分账单号：{}，订单ID：{}", billProfitSharingOrderDTO.getProfitSharingNo(), billProfitSharingOrderDTO.getOrderId(), e);
            // 更新分账状态为失败
            updateProfitSharingStatusToFailed(billProfitSharings, e.getMessage());
        }
    }

    /**
     * 构建分账请求参数
     *
     * @param billProfitSharings
     * @param unfreezeUnsplit
     * @return
     */
    private ProfitSharingOrder castProfitSharingInfoHuiFuByBillProfitSharing(List<BillProfitSharing> billProfitSharings, boolean unfreezeUnsplit) {
        ProfitSharingOrder profitSharingOrder = new ProfitSharingOrder();
        BillProfitSharing billProfitSharing = billProfitSharings.get(NumberConstant.ZERO);
        profitSharingOrder.setAppid(billProfitSharing.getAppId());
        profitSharingOrder.setTransaction_id(billProfitSharing.getTransactionId());
        profitSharingOrder.setOut_order_no(billProfitSharing.getOutTradeNo());
        profitSharingOrder.setOrgReqDate(billProfitSharing.getCreateTime().format(DateTimeFormatter.BASIC_ISO_DATE));
        // 分账角色按照分账huifuId进行聚合
        Map<String, List<BillProfitSharing>> billProfitSharingMap = billProfitSharings.stream().collect(Collectors.groupingBy(BillProfitSharing::getAccount));
        List<Receiver> receivers = billProfitSharingMap.values().stream().map(billProfitSharingList -> {
            BillProfitSharing profitSharing = billProfitSharingList.get(NumberConstant.ZERO);
            Receiver receiver = new Receiver();
            receiver.setType(profitSharing.getType());
            receiver.setHuifuId(profitSharing.getHuifuId());
            BigDecimal profitSharingPrice = billProfitSharingList.stream().map(BillProfitSharing::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            long cent = new Money(profitSharingPrice).getCent();
            receiver.setAmount((int) cent);
            receiver.setDescription(profitSharing.getDescription());
            return receiver;
        }).collect(Collectors.toList());
        profitSharingOrder.setReceivers(receivers);
        profitSharingOrder.setUnfreeze_unsplit(unfreezeUnsplit);
        return profitSharingOrder;
    }

    /**
     * 构建智付分账请求
     */
    private UnifiedProfitSharingRequest buildDinPayProfitSharingRequest(List<BillProfitSharing> billProfitSharings, PaymentDTO paymentDTO, BillProfitSharingOrderDTO billProfitSharingOrderDTO) {
        // 1. 获取渠道配置
        ChannelConfig channelConfig = getDinPayChannelConfig(paymentDTO.getPaymentChannelId());

        // 2. 构建分账规则列表
        List<SplitRule> splitRules = new ArrayList<>();
        for (BillProfitSharing billProfitSharing : billProfitSharings) {
            // 比如100元订单，进入收款户去除手续费后是99，如果需要分账仅需要划分给别人即可，与汇付不同
            if (AccountTypeEnum.TENANT.getType().equals(billProfitSharing.getAccountType())) {
                log.info("商户号与接收方相同，跳过分账，详细信息:{}", JSON.toJSONString(billProfitSharing));
                continue;
            }
            SplitRule splitRule = SplitRule.builder()
                    .merchantNo(billProfitSharing.getAccount())
                    .amount(billProfitSharing.getPrice().multiply(new BigDecimal("100")))
                    .description(billProfitSharing.getDescription())
                    .build();
            splitRules.add(splitRule);
        }

        // 3. 构建统一分账请求
        return UnifiedProfitSharingRequest.builder()
                .profitSharingNo(billProfitSharingOrderDTO.getProfitSharingNo())
                .paymentNo(paymentDTO.getPaymentNo())
                .payOrderType("APPPAY")
                .splitRules(splitRules)
                .description("订单分账-" + billProfitSharingOrderDTO.getOrderId())
                .proEnv(SpringContextUtil.isPro())
                .tenantId(billProfitSharingOrderDTO.getTenantId())
                .businessLine(PaymentBusinessLineEnums.SAAS.getCode())
                .channelConfig(channelConfig)
                .build();
    }

    /**
     * 获取智付渠道配置
     */
    private ChannelConfig getDinPayChannelConfig(Long paymentChannelId) {
        ChannelConfig channelConfigById = paymentChannelFacade.getChannelConfigById(paymentChannelId);
        if (channelConfigById == null) {
            throw new BizException("支付渠道不存在，channelId: " + paymentChannelId);
        }
        return channelConfigById;
    }

    /**
     * 处理智付分账结果
     */
    private void handleDinPayProfitSharingResult(List<BillProfitSharing> billProfitSharings, UnifiedProfitSharingResult result, BillProfitSharingOrderDTO billProfitSharingOrderDTO) {
        if (result == null) {
            log.error("智付分账结果为空，分账单号：{}", billProfitSharingOrderDTO.getProfitSharingNo());
            updateProfitSharingStatusToFailed(billProfitSharings, "分账结果为空");
            return;
        }

        if (result.getSuccess()) {
            log.info("智付分账调用成功，分账单号：{}，渠道交易ID：{}", billProfitSharingOrderDTO.getProfitSharingNo(), result.getChannelProfitSharingId());

            // 根据智付分账状态更新本地状态
            ProfitSharingStatus profitSharingStatus = result.getStatus();
            Integer localStatus = mapDinPayProfitSharingStatusToLocal(profitSharingStatus);

            // 更新分账明细状态
            for (BillProfitSharing billProfitSharing : billProfitSharings) {
                billProfitSharing.setStatus(localStatus);
                if (profitSharingStatus.isSuccess()) {
                    billProfitSharing.setSuccessTime(new Date());
                }
                // 更新数据库
                billProfitSharingService.updateByPrimaryKeySelective(billProfitSharing);
            }

            // 更新分账订单状态
            updateProfitSharingOrderStatus(billProfitSharingOrderDTO, localStatus);

        } else {
            log.error("智付分账调用失败，分账单号：{}，错误信息：{}",
                    billProfitSharingOrderDTO.getProfitSharingNo(),
                    result.getError() != null ? result.getError().getDetailMessage() : "未知错误", new ProviderException(String.format("智付分账接口调用失败，%s", result.getError().getDetailMessage())));

            String failReason = result.getError() != null ? result.getError().getDetailMessage() : "智付分账调用失败";
            updateProfitSharingStatusToFailed(billProfitSharings, failReason);
        }
    }

    /**
     * 将智付分账状态映射为本地状态
     */
    private Integer mapDinPayProfitSharingStatusToLocal(ProfitSharingStatus dinPayStatus) {
        switch (dinPayStatus) {
            case SUCCESS:
                return ProfitSharingResultEnum.FINISHED.getStatus();
            case DOING:
                return ProfitSharingResultEnum.PROCESSING.getStatus();
            case FAILED:
                return ProfitSharingResultEnum.FAILED.getStatus();
            default:
                log.warn("未知的智付分账状态：{}", dinPayStatus);
                throw new ProviderException("未知的智付分账状态");
        }
    }

    /**
     * 更新分账状态为失败
     */
    private void updateProfitSharingStatusToFailed(List<BillProfitSharing> billProfitSharings, String failReason) {
        for (BillProfitSharing billProfitSharing : billProfitSharings) {
            billProfitSharing.setStatus(ProfitSharingResultEnum.FAILED.getStatus());
            billProfitSharing.setFailReason(failReason);
            billProfitSharingService.updateByPrimaryKeySelective(billProfitSharing);
        }
    }

    /**
     * 查询并更新智付分账状态
     */
    @Override
    public void queryAndUpdateDinPayProfitSharingStatus(List<BillProfitSharing> billProfitSharings, PaymentDTO paymentDTO, BillProfitSharingOrderDTO billProfitSharingOrderDTO) {
        try {
            log.info("开始查询智付分账状态 - 分账单号：{}", billProfitSharingOrderDTO.getProfitSharingNo());

            // 1. 构建查询请求
            UnifiedQueryProfitSharingRequest queryRequest = buildDinPayProfitSharingQueryRequest(paymentDTO, billProfitSharingOrderDTO);

            // 2. 调用智付分账查询接口
            UnifiedQueryProfitSharingResult queryResult = paymentClientService.queryProfitSharing(queryRequest);

            // 3. 处理查询结果
            handleDinPayProfitSharingQueryResult(billProfitSharings, queryResult, billProfitSharingOrderDTO);

            log.info("智付分账状态查询完成 - 分账单号：{}，查询结果：{}",
                    billProfitSharingOrderDTO.getProfitSharingNo(),
                    queryResult != null ? queryResult.getStatus() : "null");

        } catch (Exception e) {
            log.error("智付分账状态查询失败，分账单号：{}，订单ID：{}",
                    billProfitSharingOrderDTO.getProfitSharingNo(),
                    billProfitSharingOrderDTO.getOrderId(), e);
        }
    }

    /**
     * 构建智付分账查询请求
     */
    private UnifiedQueryProfitSharingRequest buildDinPayProfitSharingQueryRequest(PaymentDTO paymentDTO, BillProfitSharingOrderDTO billProfitSharingOrderDTO) {
        // 1. 获取渠道配置
        ChannelConfig channelConfig = getDinPayChannelConfig(paymentDTO.getPaymentChannelId());

        // 2. 构建查询请求
        return UnifiedQueryProfitSharingRequest.builder()
                .profitSharingNo(billProfitSharingOrderDTO.getProfitSharingNo())
                .paymentNo(paymentDTO.getPaymentNo())
                .proEnv(SpringContextUtil.isPro())
                .tenantId(billProfitSharingOrderDTO.getTenantId())
                .businessLine(PaymentBusinessLineEnums.SAAS.getCode())
                .channelConfig(channelConfig)
                .build();
    }

    /**
     * 处理智付分账查询结果
     */
    private void handleDinPayProfitSharingQueryResult(List<BillProfitSharing> billProfitSharings, UnifiedQueryProfitSharingResult queryResult, BillProfitSharingOrderDTO billProfitSharingOrderDTO) {
        if (queryResult == null) {
            log.warn("智付分账查询结果为空，分账单号：{}", billProfitSharingOrderDTO.getProfitSharingNo());
            return;
        }

        if (queryResult.getSuccess()) {
            ProfitSharingStatus profitSharingStatus = queryResult.getStatus();
            log.info("智付分账查询成功，分账单号：{}，状态：{}",
                    billProfitSharingOrderDTO.getProfitSharingNo(), profitSharingStatus);

            // 根据查询到的状态更新本地状态
            Integer localStatus = mapDinPayProfitSharingStatusToLocal(profitSharingStatus);

            // 更新分账明细状态
            for (BillProfitSharing billProfitSharing : billProfitSharings) {
                // 只有当查询到的状态比当前状态更"终态"时才更新
                if (shouldUpdateStatus(billProfitSharing.getStatus(), localStatus)) {
                    billProfitSharing.setStatus(localStatus);

                    // 如果是成功状态，设置成功时间
                    if (profitSharingStatus != null && profitSharingStatus.isSuccess()) {
                        billProfitSharing.setSuccessTime(new Date());
                    }

                    // 如果是失败状态，设置失败原因
                    if (profitSharingStatus != null && profitSharingStatus.isFailed()) {
                        String failReason = queryResult.getError() != null ?
                                queryResult.getError().getDetailMessage() : "智付分账失败";
                        billProfitSharing.setFailReason(failReason);
                    }

                    // 更新数据库
                    billProfitSharingService.updateByPrimaryKeySelective(billProfitSharing);

                    log.info("更新分账明细状态 - ID：{}，状态：{} -> {}",
                            billProfitSharing.getId(), billProfitSharing.getStatus(), localStatus);
                }
            }

            // 更新分账订单状态
            updateProfitSharingOrderStatus(billProfitSharingOrderDTO, localStatus);

        } else {
            log.error("智付分账查询失败，分账单号：{}，错误信息：{}",
                    billProfitSharingOrderDTO.getProfitSharingNo(),
                    queryResult.getError() != null ? queryResult.getError().getDetailMessage() : "未知错误");
        }
    }

    /**
     * 判断是否应该更新状态
     * 只有当新状态比当前状态更"终态"时才更新
     */
    private boolean shouldUpdateStatus(Integer currentStatus, Integer newStatus) {
        // 状态优先级：失败(2) > 完成(1) > 处理中/待分账(0)
        if (currentStatus == null) {
            return true;
        }

        // 如果当前已经是失败状态，不再更新
        if (Objects.equals(currentStatus, ProfitSharingResultEnum.FAILED.getStatus())) {
            return false;
        }

        // 如果当前已经是完成状态，只有失败状态才能覆盖
        if (Objects.equals(currentStatus, ProfitSharingResultEnum.FINISHED.getStatus())) {
            return Objects.equals(newStatus, ProfitSharingResultEnum.FAILED.getStatus());
        }

        // 其他情况都可以更新
        return true;
    }

    /**
     * 更新分账订单状态
     */
    private void updateProfitSharingOrderStatus(BillProfitSharingOrderDTO billProfitSharingOrderDTO, Integer status) {
        // 根据分账明细状态决定分账订单状态
        BillProfitSharingOrderStatusEnum orderStatus;
        if (Objects.equals(status, ProfitSharingResultEnum.FINISHED.getStatus())) {
            orderStatus = BillProfitSharingOrderStatusEnum.FINISHED;
        } else if (Objects.equals(status, ProfitSharingResultEnum.FAILED.getStatus())) {
            orderStatus = BillProfitSharingOrderStatusEnum.FAILED;
        } else {
            orderStatus = BillProfitSharingOrderStatusEnum.PROCESSING;
        }

        billProfitSharingOrderService.updateStatusById(
                billProfitSharingOrderDTO.getId(),
                orderStatus.getStatus(),
                BillProfitSharingOrderStatusEnum.PROCESSING.getStatus()
        );
    }

    @Override
    public ResultDTO queryProfitSharingResult(Long tenantId, Long orderId) {
        // 查询分账记录
        List<BillProfitSharing> billProfitSharings = billProfitSharingMapper.querySuccessByTenantIdAndOrderId(tenantId, orderId, ProfitSharingBusinessType.FORWARD_DIRECTION.getCode());
        if (CollectionUtils.isEmpty(billProfitSharings)) {
            log.info("{}订单没有生成分账单", orderId);
        }

        BillProfitSharing billProfitSharing = billProfitSharings.get(NumberConstant.ZERO);
        // 处理查询参数
        QueryOrderParams queryOrderParams = new QueryOrderParams();
        queryOrderParams.setTransactionId(billProfitSharing.getTransactionId());
        queryOrderParams.setOutOrderNo(billProfitSharing.getOutTradeNo());
        ResultDTO<ProfitSharingOrderResult> profitSharingOrderResultResultDTO = handleProfitSharingOrderResult(queryOrderParams, billProfitSharings);
        return profitSharingOrderResultResultDTO;
    }

    @Override
    public String huiFuPayNotification(HttpServletRequest request) {
        // 验签请参data
        String data = request.getParameter("resp_data");

        HuiFuiPaymentReceive huiFuPaymentReceive = JSONObject.parseObject(data, HuiFuiPaymentReceive.class);
        String huifuId = huiFuPaymentReceive.getHuifu_id();
        HuiFuPayment huiFuPaymentQuery = huiFuPaymentMapper.selectOne(new LambdaQueryWrapper<HuiFuPayment>().eq(HuiFuPayment::getHfSeqId, huiFuPaymentReceive.getHf_seq_id()));
        if (Objects.isNull(huiFuPaymentQuery)) {
            huiFuPaymentQuery = huifuPaymentService.selectByReqSeqId(huiFuPaymentReceive.getReq_seq_id(), huiFuPaymentReceive.getHuifu_id());
        }
        if (NotifyTypeEnum.ALREADY.getCode().equals(huiFuPaymentQuery.getNotifyFlag())) {
            return "RECV_ORD_ID_" + huiFuPaymentReceive.getReq_seq_id();
        }

        String publicKey = tenantAuthConnectionService.selectPublicKey(huifuId);
        // 验签请参sign
        String sign = request.getParameter("sign");
        log.info("huiFuPayNotification sign：{}，data：{}", sign, data);
        // 使用汇付公钥验签
        boolean isLegal = verifyNotifyLegal(data, publicKey, sign, huiFuPaymentReceive);
        if (!isLegal) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }

        String subRespCode = huiFuPaymentReceive.getResp_code();
        if ("00000000".equals(subRespCode)) {
            log.info("汇付支付异步返回消息:{}", JSONObject.toJSONString(huiFuPaymentReceive));
            paymentService.huifuPaySuccess(huiFuPaymentReceive, huiFuPaymentQuery.getPaymentId());
            log.info("RECV_ORD_ID_{}", huiFuPaymentReceive.getReq_seq_id());
            return "RECV_ORD_ID_" + huiFuPaymentReceive.getReq_seq_id();
        } else {
            log.warn("汇付支付异步返回状态码异常:{}", JSONObject.toJSONString(huiFuPaymentReceive));
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }
    }

    /**
     * 验证回调合法性
     *
     * @param data      回调数据
     * @param publicKey 汇付公钥
     * @param sign      签名
     * @return
     */
    private boolean verifyNotifyLegal(String data, String publicKey, String sign, HuiFuiPaymentReceive huiFuiPaymentReceive) {
        String hfSeqId = huiFuiPaymentReceive.getReq_seq_id();
        if (SignatureUtil.verify(data, publicKey, sign)) {
            log.info("{}验签成功", hfSeqId);
            return true;
        }
        log.error("验签失败", new ProviderException(String.format("支付单: %s, 验签失败, 即将主动查询汇付验证合法性", hfSeqId)));
        Payment payment = paymentMapper.selectByPaymentNo(hfSeqId);
        if (Objects.isNull(payment)) {
            log.error("未查询到支付单信息", new ProviderException(String.format("支付回调未查询到支付单信息: %s", hfSeqId)));
            return false;
        }
        PaymentResult paymentResult = queryExternalPaymentStatus(payment);
        if (paymentResult == null) {
            log.error("未查询到支付单信息", new ProviderException(String.format("查询不到对应外部支付单的信息: %s", hfSeqId)));
            return false;
        }
        OrderPayResultDTO orderPayResultDTO = paymentResult.getOrderPayResultDTO();
        if (orderPayResultDTO == null) {
            log.error("未查询到支付单信息", new ProviderException(String.format("查询不到对应外部支付单的信息: %s", hfSeqId)));
            return false;
        }
        // 从汇付查询到的支付单状态
        String transStat = orderPayResultDTO.getTrans_stat();
        String notifyTransStat = huiFuiPaymentReceive.getTrans_stat();
        if (Objects.equals(transStat, notifyTransStat)) {
            log.info("支付单:{}主动查询汇付验证合法性成功", hfSeqId);
            return true;
        }
        log.error("支付单:{}主动查询汇付验证合法性失败", hfSeqId);
        return false;
    }

    /**
     * 汇付支付成功结果处理
     *
     * @param huiFuPaymentReceive
     */
//    @Transactional(rollbackFor = Exception.class)
    @Override
    public void huifuPaySuccess(HuiFuiPaymentReceive huiFuPaymentReceive, Long paymentId) {
        Payment payment = paymentMapper.selectByPrimaryKey(paymentId);

        // 判断支付单是否成功，如果成功不再重复更新
        TenantAuthConnectionDTO tenantAuthConnectionDTO = tenantAuthConnectionService.selectByTenantId(payment.getTenantId());
        // 费用为空，就要等待费率返回计算；不为空，异步无需等待
        boolean feeAmountNull = StringUtils.isEmpty(huiFuPaymentReceive.getFee_amount());

        // 异步查询支付费率
        CompletableFuture<String> feeRateFuture = asyncQueryFeeRate(tenantAuthConnectionDTO, payment, feeAmountNull, huiFuPaymentReceive);

        // 查询支付单的PaymentItem列表
        List<PaymentItem> itemList = paymentItemMapper.selectByPaymentId(payment.getId());
        boolean transactionResult = paymentService.huifuPaySuccessInTransaction(huiFuPaymentReceive, payment, itemList, feeAmountNull, feeRateFuture);

        // 支付mq通知支付结果
        if (transactionResult) {
            sendPayNotifyMessage(payment.getPaymentNo());
        }
    }

    /**
     * 发送支付成功消息
     *
     * @param paymentNo
     */
    private void sendPayNotifyMessage(String paymentNo) {
        PayNotifyMessageDTO payNotifyDTO = new PayNotifyMessageDTO();
        payNotifyDTO.setPaymentNo(paymentNo);
        mqProducer.sendOrderly(MQTopicConstant.TOPIC_SAAS_PAYMENT_NOTIFY, null, payNotifyDTO, paymentNo);
    }

    /**
     * 异步查询
     *
     * @param tenantAuthConnectionDTO
     * @param payment
     * @param feeAmountNull
     * @return
     */
    private CompletableFuture<String> asyncQueryFeeRate(TenantAuthConnectionDTO tenantAuthConnectionDTO, Payment payment, boolean feeAmountNull, HuiFuiPaymentReceive huiFuiPaymentReceive) {
        // 若费用不为空,下游不会更新费率字段，所以这里插入数据保证一定处理费率
        if (!feeAmountNull) {
            HuiFuPaymentRateRetry huiFuPaymentRateRetry = new HuiFuPaymentRateRetry();
            huiFuPaymentRateRetry.setPaymentId(payment.getId());
            huiFuPaymentRateRetry.setTenantId(payment.getTenantId());
            huiFuPaymentRateRetryService.save(huiFuPaymentRateRetry);
        }
        String tradeType = huiFuiPaymentReceive.getTrade_type();
        return CompletableFuture.supplyAsync(
                () -> {
                    // http请求获取费率
                    long startTime = System.currentTimeMillis();
                    SettleMentInfoDTO settleMentInfoDTO = HuiFuApi.queryMerchantBasicData(tenantAuthConnectionDTO, huiFuConfig);
                    log.info("查询支付费率耗时:{}ms,支付单信息:{},返回:{}", System.currentTimeMillis() - startTime, JSON.toJSONString(payment), JSON.toJSONString(settleMentInfoDTO));

                    String feeRate = null;
                    if (Objects.equals(tradeType, TradeTypeEnum.A_NATIVE.getDesc())) {
                        List<QryAliConfDTO> qryAliConfDTO = settleMentInfoDTO.getQryAliConfDTO();
                        for (QryAliConfDTO aliConfDTO : qryAliConfDTO) {
                            if (Objects.equals(aliConfDTO.getPay_scene(), AliPaySceneEnum.ONLINE_SCAN.getCode())) {
                                feeRate = aliConfDTO.getFee_rate();
                            }
                        }
                    } else {
                        List<QryWxConfDTO> qryWxConfDTOList = settleMentInfoDTO.getQryWxConfDTO();
                        for (QryWxConfDTO qryWxConfDTO : qryWxConfDTOList) {
                            if (WxPaySceneEnum.OFFLINE_MIMI_PROGRAM.getCode().equals(qryWxConfDTO.getPay_scene())) {
                                feeRate = qryWxConfDTO.getFee_rate();
                            }
                        }
                    }
                    log.info("异步获取支付单:{}，费率:{}，交易类型:{}", payment.getPaymentNo(), feeRate, tradeType);

                    // 若费用不为空,更新费率,删除重试数据
                    if (!feeAmountNull) {
                        String finalFeeRate = feeRate;
                        transactionTemplate.execute(status -> {
                            Boolean result = true;
                            try {
                                // 更新支付费率信息、删除重试数据
                                Payment updatePayment = new Payment();
                                updatePayment.setId(payment.getId());
                                updatePayment.setFeeRate(new BigDecimal(finalFeeRate));
                                // 乐观更新
                                paymentMapper.updateFeeByPrimaryKeyCas(updatePayment);

                                // 如果是组合支付 也更新下子单
                                if (Objects.equals(payment.getTradeType(), TradeTypeEnum.COMBINED_PAY.getDesc())) {
                                    String paymentNo = payment.getPaymentNo();
                                    List<PaymentCombinedDetail> details = paymentCombinedDetailService.selectByCombinedPaymentNo(paymentNo);
                                    details.stream().filter(el -> Objects.equals(el.getOnlinePayChannel(), OnlinePayChannelEnum.HUIFU_PAY.getChannel()))
                                            .forEach(paymentCombinedDetail -> {
                                                PaymentCombinedDetail update = new PaymentCombinedDetail();
                                                update.setId(paymentCombinedDetail.getId());
                                                update.setFeeRate(new BigDecimal(finalFeeRate));
                                                paymentCombinedDetailService.updateByPrimaryKeySelective(update);
                                            });
                                }
                                huiFuPaymentRateRetryService.deleteByPaymentId(payment.getId());

                            } catch (Exception e) {
                                log.error("支付单{}更新支付费率信息失败", payment.getId());
                                status.setRollbackOnly();
                                result = false;
                            }
                            return result;
                        });
                    }
                    return feeRate;
                }
                , orderNotifyExecutorService);
    }

    /**
     * 从future中获取返回结果
     *
     * @param huiFuPaymentReceive
     * @param feeRateFuture
     * @return
     */
    private String getFeeRate(HuiFuiPaymentReceive huiFuPaymentReceive, CompletableFuture<String> feeRateFuture) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String feeRate = null;
        try {
            return feeRateFuture.get();
        } catch (InterruptedException e) {
            log.error("获取费率异常,e", e);
        } catch (ExecutionException e) {
            log.error("获取费率异常,e", e);
        } finally {
            stopWatch.stop();
            log.info("查询支付费率等待耗时:{}ms,数据:{}", stopWatch.getTotalTimeMillis(), JSON.toJSONString(huiFuPaymentReceive));
        }
        return feeRate;
    }

    /**
     * 事务操作
     *
     * @param huiFuPaymentReceive
     * @param payment
     * @param itemList
     * @param feeAmountNull       费用是否为空
     * @param feeRateFuture
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean huifuPaySuccessInTransaction(HuiFuiPaymentReceive huiFuPaymentReceive, Payment payment, List<PaymentItem> itemList,
                                             boolean feeAmountNull, CompletableFuture<String> feeRateFuture) {
        // 锁定所有的支付单
        List<Payment> payments = lockOrderAllPayments(payment.getPaymentNo());
        // 处理支付幂等
        boolean noCallBackHasProcessed = noCallBackHasProcessed(payment, payments);
        if (!noCallBackHasProcessed) {
            return false;
        }

        // 更新汇付支付信息
        HuiFuPayment huiFuPayment = updateHuiFuPayment(huiFuPaymentReceive, payment.getId());

        // 更新支付信息
        payment.setTransactionId(huiFuPaymentReceive.getOut_trans_id());
        // 支付单原状态为微信小程序插件，不进行更新tradeType
        if (!TradeTypeEnum.HF_WECHAT_PLUGIN.getDesc().equals(payment.getTradeType()) && !TradeTypeEnum.COMBINED_PAY.getDesc().equals(payment.getTradeType())) {
            payment.setTradeType(huiFuPaymentReceive.getTrans_type());
        }
        payment.setTradeState(huiFuPaymentReceive.getTrans_stat());
        payment.setTradeStateDesc(huiFuPaymentReceive.getBank_message());
        if (Objects.nonNull(huiFuPaymentReceive.getWx_response())) {
            WxResponse wxResponse = JSONObject.parseObject(huiFuPaymentReceive.getWx_response(), WxResponse.class);
            payment.setSpOpenid(wxResponse.getOpenid());
            payment.setBankType(wxResponse.getBank_type());
        }

        payment.setSuccessTime(LocalDateTime.parse(huiFuPaymentReceive.getEnd_time(), DateTimeFormatter.ofPattern(TimeUtils.FORMAT_TIME_STAMP)));
        payment.setFinishTime(LocalDateTime.now());
        // 获取支付状态
        PaymentEnum.Status status = PaymentEnum.Status.getStatusByHuifuStat(huiFuPaymentReceive.getTrans_stat());
        payment.setStatus(status.getCode());

        // 费率
        String feeRate;
        // 获取汇付返回手续费
        String feeAmount = huiFuPayment.getFeeAmount();
        if (feeAmountNull) {
            // 等待查询费率响应
            feeRate = getFeeRate(huiFuPaymentReceive, feeRateFuture);
            payment.setFeeRate(new BigDecimal(feeRate));
        } else {
            // fee_rate是百分率，所以这里需要乘以一百，便于后续统一处理
            BigDecimal rate = NumberUtil.div(new BigDecimal(feeAmount), payment.getTotalPrice());
            feeRate = NumberUtil.mul(rate, NumberConstant.HUNDRED).setScale(NumberConstant.TWO, ROUND_HALF_UP).toString();
            log.info("paymentId:{},计算所得费率 fee_rate:{}，feeAmount：{}，totalPrice:{}", payment.getId(), feeRate, feeAmount, payment.getTotalPrice());
        }

        paymentMapper.updateByPrimaryKeySelective(payment);

        // 如果是组合支付，则更新子单
        if (Objects.equals(payment.getTradeType(), TradeTypeEnum.COMBINED_PAY.getDesc())) {
            String paymentNo = payment.getPaymentNo();
            List<PaymentCombinedDetail> details = paymentCombinedDetailService.selectByCombinedPaymentNo(paymentNo);
            PaymentCombinedDetail combinedDetail = details.stream().filter(el -> Objects.equals(el.getOnlinePayChannel(), OnlinePayChannelEnum.HUIFU_PAY.getChannel()))
                    .findFirst().orElseThrow(() -> new ProviderException("未查询到支付子单"));

            PaymentCombinedDetail update = getHfCombinedDetail(huiFuPaymentReceive, payment, combinedDetail);
            paymentCombinedDetailService.updateByPrimaryKeySelective(update);

            PaymentCombinedDetail nativeCombinedDetail = details.stream()
                    .filter(el -> Objects.isNull(el.getOnlinePayChannel()))
                    .findFirst()
                    .orElseThrow(() -> new ProviderException("未查询到本地支付子单"));
            paymentCombinedDetailService.decreaseFreezeAmount(nativeCombinedDetail);
        }
        return true;
    }

    private static PaymentCombinedDetail getHfCombinedDetail(HuiFuiPaymentReceive huiFuPaymentReceive, Payment payment, PaymentCombinedDetail combinedDetail) {
        PaymentCombinedDetail update = new PaymentCombinedDetail();
        update.setId(combinedDetail.getId());
        update.setTransactionId(payment.getTransactionId());
        // 支付单原状态为微信小程序插件，不进行更新tradeType
        if (!TradeTypeEnum.HF_WECHAT_PLUGIN.getDesc().equals(combinedDetail.getTradeType())) {
            update.setTradeType(huiFuPaymentReceive.getTrans_type());
        }
        update.setTradeState(payment.getTradeState());
        update.setTradeStateDesc(payment.getTradeStateDesc());
        update.setSpOpenid(payment.getSpOpenid());
        update.setBankType(payment.getBankType());
        update.setSuccessTime(payment.getSuccessTime());
        update.setFinishTime(payment.getFinishTime());
        update.setStatus(payment.getStatus());
        update.setFeeRate(payment.getFeeRate());
        return update;
    }

    /**
     * 更新汇付支付信息
     *
     * @param huiFuPaymentReceive
     */
    public HuiFuPayment updateHuiFuPayment(HuiFuiPaymentReceive huiFuPaymentReceive, Long paymentId) {
        HuiFuPayment huiFuPayment = new HuiFuPayment();
        huiFuPayment.setRespCode(huiFuPaymentReceive.getResp_code());
        huiFuPayment.setRespDesc(huiFuPaymentReceive.getResp_desc());
        huiFuPayment.setReqDate(huiFuPaymentReceive.getReq_date());
        huiFuPayment.setReqSeqId(huiFuPaymentReceive.getReq_seq_id());
        huiFuPayment.setHfSeqId(huiFuPaymentReceive.getHf_seq_id());
        huiFuPayment.setTradeType(huiFuPaymentReceive.getTrans_type());
        huiFuPayment.setTransAmt(huiFuPaymentReceive.getTrans_amt());
        huiFuPayment.setTransStat(huiFuPaymentReceive.getTrans_stat());
        huiFuPayment.setHuifuId(huiFuPaymentReceive.getHuifu_id());
        huiFuPayment.setBankCode(huiFuPaymentReceive.getBank_code());
        huiFuPayment.setBankMessage(huiFuPaymentReceive.getBank_message());
        huiFuPayment.setDelayAcctFlag(huiFuPaymentReceive.getDelay_acct_flag());
        huiFuPayment.setPayInfo(huiFuPaymentReceive.getPay_info());
        huiFuPayment.setQrCode(huiFuPaymentReceive.getQr_code());
        huiFuPayment.setAlipayResponse(huiFuPaymentReceive.getAlipay_response());
        huiFuPayment.setWxResponse(huiFuPaymentReceive.getWx_response());
        huiFuPayment.setUnionpayResponse(huiFuPaymentReceive.getUnionpay_response());
        huiFuPayment.setRemark(huiFuPaymentReceive.getRemark());
        huiFuPayment.setAcctId(huiFuPaymentReceive.getAcct_id());
        huiFuPayment.setSettlementAmt(huiFuPaymentReceive.getSettlement_amt());
        huiFuPayment.setFeeFlag(huiFuPaymentReceive.getFee_flag());
        huiFuPayment.setFeeAmount(huiFuPaymentReceive.getFee_amount());
        huiFuPayment.setTransFinshTime(huiFuPaymentReceive.getTrans_finsh_time());
        huiFuPayment.setAcctDate(huiFuPaymentReceive.getAcct_date());
        huiFuPayment.setAcctSplitBunch(huiFuPaymentReceive.getAcct_split_bunch());
        huiFuPayment.setMerDevLocation(huiFuPaymentReceive.getMer_dev_location());
        huiFuPayment.setTransFeeAllowanceInfo(huiFuPaymentReceive.getTrans_fee_allowance_info());
        huiFuPayment.setCombinedpayData(huiFuPaymentReceive.getCombinedpay_data());
        huiFuPayment.setCombinedpayFeeAmt(huiFuPaymentReceive.getCombinedpay_fee_amt());
        huiFuPayment.setNotifyFlag(NotifyTypeEnum.ALREADY.getCode());
        huiFuPayment.setPartyOrderId(huiFuPaymentReceive.getParty_order_id());
        int result = huifuPaymentService.updateByHfSeqId(huiFuPayment, huiFuPaymentReceive.getHf_seq_id());
        if (result == 0) {
            // 汇付插件预下单，没有存储全局流水号，所以需要根据支付单ID更新
            result = huifuPaymentService.updateByPaymentId(huiFuPayment, paymentId);
        }
        if (result <= 0) {
            log.error("更新汇付支付信息失败,paymentId:{},huiFuPaymentReceive:{}", paymentId, JSON.toJSONString(huiFuPaymentReceive), new Exception("支付回调更新汇付支付信息异常"));
        }
        return huiFuPayment;
    }

    @Override
    public PaymentDTO querySuccessByOrderId(Long tenantId, Long orderId) {
        PaymentItem paymentItem = paymentItemMapper.selectPaySuccessByOrderId(tenantId, orderId);
        if (Objects.isNull(paymentItem)) {
            return null;
        }

        Payment payment = paymentMapper.selectByPrimaryKey(paymentItem.getPaymentId());
        PaymentDTO paymentDTO = new PaymentDTO();
        BeanUtils.copyProperties(payment, paymentDTO);
        return paymentDTO;
    }

    @Override
    public BigDecimal queryPaymentRateByOrderId(Long tenantId, Long orderId) {
        PaymentItem paymentItem = paymentItemMapper.selectPaySuccessByOrderId(tenantId, orderId);
        Payment payment = paymentMapper.selectByPrimaryKey(paymentItem.getPaymentId());
        return payment.getFeeRate();
    }


    @Override
    public PaymentResult queryExternalPaymentStatus(Payment payment) {
        PayTemplate payTemplate = payStrategyFactory.getTemplateByTradeType(payment.getTradeType());
        PaymentRequest request = PaymentRequest.builder().paymentId(payment.getId()).tenantId(payment.getTenantId()).build();
        PaymentResult paymentResult = payTemplate.queryLastPaymentResult(request);
        return paymentResult;
    }

    @Override
    public boolean closePaymentOrder(Payment payment) {
        // 关闭外部的
        closeExternalPaymentOrder(payment);

        // 关闭本地
        int size = paymentMapper.updateStatus(payment.getId(), PaymentEnum.Status.CANCELED.getCode(), payment.getStatus());
        if (size != NumberConstant.ONE) {
            log.error("支付单号：[{}] 关闭支付请求更新状态失败", payment.getPaymentNo());
            throw new BizException("当前关闭支付单失败，请稍后重试");
        }
        return true;
    }

    @Override
    public boolean closeExternalPaymentOrder(Payment payment) {
        PaymentRequest paymentRequest = new PaymentRequest();
        paymentRequest.setTenantId(payment.getTenantId());
        paymentRequest.setPaymentId(payment.getId());
        paymentRequest.setPaymentNo(payment.getPaymentNo());
        paymentRequest.setPaymentCreateTime(payment.getCreateTime());

        String tradeType = payment.getTradeType();
        PayTemplate payTemplate = payStrategyFactory.getTemplateByTradeType(tradeType);
        return payTemplate.callClosePayRequest(paymentRequest);
    }

    /**
     * 锁所有的支付单（处理中的和已成功的）
     *
     * @param paymentNo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<Payment> lockOrderAllPayments(String paymentNo) {
        if (StringUtils.isBlank(paymentNo)) {
            throw new ProviderException("支付单号不能为空");
        }
        List<PaymentItem> paymentItems = paymentItemMapper.selectByPaymentNo(paymentNo);
        if (CollectionUtils.isEmpty(paymentItems)) {
            throw new ProviderException("未查询到支付单明细信息");
        }
        Long tenantId = paymentItems.get(0).getTenantId();
        List<Long> orderIds = paymentItems.stream().map(PaymentItem::getOrderId).collect(Collectors.toList());
        List<PaymentItem> allPaymentItems = paymentItemMapper.selectByOrderIds(tenantId, orderIds);
        Set<Long> allPaymentIds = allPaymentItems.stream().map(PaymentItem::getPaymentId).collect(Collectors.toSet());
        // 根据ids查询处理中和已完成的支付单
        List<Payment> allPayments = paymentMapper.querySuccessAndInProcessPaymentsForUpdate(allPaymentIds);
        return allPayments;
    }

    private boolean noCallBackHasProcessed(Payment payment, List<Payment> payments) {
        log.info("payment：[{}], payments：[{}]", payment.toString(), payments.toString());
        Map<Integer, List<Payment>> allPaymentsMap = payments.stream().collect(Collectors.groupingBy(Payment::getStatus));
        List<Payment> successPayments = allPaymentsMap.get(PaymentEnum.Status.SUCCESS.getCode());
        if (CollectionUtils.isEmpty(successPayments)) {
            log.info("没有已成功的支付单，支付幂等流程结束");
            return true;
        }

        if (successPayments.size() > 1) {
            throw new ProviderException("支付幂等异常，请关注");
        }

        // 有已成功的支付单，且是自己
        Payment successPayment = successPayments.get(0);
        if (Objects.equals(payment.getId(), successPayment.getId())) {
            log.info("支付幂等拦截，支付单：[{}]已成功", payment.getId());
            return false;
        }

        List<Payment> dealingPayments = allPaymentsMap.get(PaymentEnum.Status.DEALING.getCode());
        if (CollectionUtils.isEmpty(dealingPayments)) {
            log.error("没有正在支付中的支付单");
            return false;
            //throw new ProviderException("没有正在支付中的支付单");
        }

        log.info("dealingPayments：[{}]", dealingPayments.toString());

        // 生成退款单
        log.error("支付幂等拦截，支付单：[{}]需要生成退款单", payment.getPaymentNo(), new ProviderException("支付幂等拦截，请及时生成退款单"));
        //createRefundByPayment(dealingPayments);

        // 更新支付单状态
        List<Long> paymentIds = dealingPayments.stream().map(Payment::getId).collect(Collectors.toList());
        int updateResult = paymentMapper.updateStatusByIds(paymentIds, PaymentEnum.Status.DUPLICATE_SUCCESS.getCode(), PaymentEnum.Status.DEALING.getCode());
        if (updateResult < paymentIds.size()) {
            log.info("updateResult：[{}], paymentIds：[{}]", updateResult, paymentIds);
            throw new ProviderException("更新支付单状态至支付成功幂等失败");
        }
        return false;
    }

    /**
     * 根据支付单生成退款单
     *
     * @param payments
     */
    public void createRefundByPayment(List<Payment> payments) {
        List<Refund> refunds = payments.stream().map(this::convertPaymentToRefund).collect(Collectors.toList());
        refundService.batchInsert(refunds);
        log.info("refundIds：[{}]退款单生成完毕", refunds.stream().map(Refund::getId).collect(Collectors.toList()));
    }

    private Refund convertPaymentToRefund(Payment payment) {
        Refund refund = new Refund();
        String refundNo = Global.generateRefundNo();
        refund.setTenantId(payment.getTenantId());
        refund.setPaymentId(payment.getId());
        refund.setRefundNo(refundNo);
        refund.setRefundPrice(payment.getTotalPrice());
        refund.setCreateTime(LocalDateTime.now());
        refund.setRefundStatus(RefundEnum.Status.CREATE_REFUND.getStatus());
        refund.setPaymentPrice(payment.getTotalPrice());
        refund.setSubMchid(payment.getSpMchid());
        refund.setRetryNum(0);
        return refund;
    }

    @Override
    public Payment queryById(Long id) {
        return paymentMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<Payment> querySuccessPayments(Collection<Long> tenantIds, String startTime, String endTime) {
        return paymentMapper.querySuccessPaymentsByTenantId(tenantIds, startTime, endTime);
    }

    @Override
    public Payment queryPayment(Long orderId, Long tenantId) {
        PaymentItem paymentItem = paymentItemMapper.selectByOrderId(tenantId, orderId);
        if (Objects.isNull(paymentItem)) {
            return null;
        }

        return paymentMapper.selectByPrimaryKey(paymentItem.getPaymentId());
    }

    @Override
    public Payment queryByRefundId(Long refundId) {
        Refund refund = refundMapper.selectByPrimaryKey(refundId);
        if (refund == null) {
            throw new ParamsException("未查询到退款单信息");
        }
        Long tenantId = refund.getTenantId();
        Long paymentId = refund.getPaymentId();
        if (paymentId != null) {
            return paymentMapper.selectByPrimaryKey(paymentId);
        }
        if (refund.getAfterSaleId() != null) {
            Long afterSaleId = refund.getAfterSaleId();
            List<OrderAfterSaleResp> orderAfterSales = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryByIds(Collections.singletonList(afterSaleId)));
            if (CollectionUtils.isEmpty(orderAfterSales)) {
                throw new ProviderException("生成交易流水，未查询到售后订单号");
            }
            OrderAfterSaleResp orderAfterSale = orderAfterSales.get(0);
            Long orderId = orderAfterSale.getOrderId();
            PaymentItem paymentItem = paymentItemMapper.selectByOrderId(tenantId, orderId);
            if (paymentItem == null) {
                log.warn("未查询到支付单信息, refundId:{}", refundId);
                return null;
            }
            return paymentMapper.selectByPrimaryKey(paymentItem.getPaymentId());
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean dinNotifyPaySuccess(DinPayNotifyDTO dinNotifyDTO) {
        String paymentNo = dinNotifyDTO.getOrderNo();
        Payment payment = paymentMapper.selectByPaymentNo(paymentNo);
        // 锁定所有的支付单
        List<Payment> payments = lockOrderAllPayments(payment.getPaymentNo());
        // 处理支付幂等
        boolean noCallBackHasProcessed = noCallBackHasProcessed(payment, payments);
        if (!noCallBackHasProcessed) {
            return false;
        }

        PaymentAttachInfoDTO attachInfoDTO = JSONObject.parseObject(dinNotifyDTO.getOrderDesc(), PaymentAttachInfoDTO.class);
        Long paymentChannelId = attachInfoDTO.getPaymentChannelId();
        payment.setPaymentChannelId(paymentChannelId);

        // 更新支付信息
        payment.setTransactionId(dinNotifyDTO.getOutTransactionOrderId());
        if (!TradeTypeEnum.COMBINED_PAY.getDesc().equals(payment.getTradeType())) {
            payment.setTradeType(dinNotifyDTO.getPaymentType().concat("(").concat(dinNotifyDTO.getPaymentMethods()).concat(")"));
        }
        payment.setTradeState(dinNotifyDTO.getOrderStatus());
        payment.setSpOpenid(dinNotifyDTO.getOpenid());
        payment.setBankType(dinNotifyDTO.getBankType());
        LocalDateTime successTime = DateUtil.parse(dinNotifyDTO.getOrderPayDate()).toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime();
        payment.setSuccessTime(successTime);
        payment.setFinishTime(LocalDateTime.now());
        // 获取支付状态
        PaymentEnum.Status status = PaymentEnum.Status.getStatusByDinStat(dinNotifyDTO.getOrderStatus());
        if (status == null) {
            log.error("支付回调，未知的智付状态: {}", dinNotifyDTO.getOrderStatus());
            return false;
        }
        payment.setStatus(status.getCode());
        if (dinNotifyDTO.getMerchantFee() != null) {
            payment.setFeeRate(NumberUtil.mul(dinNotifyDTO.getMerchantFee(), new BigDecimal("100")));
        } else {
            log.error("支付单号:{}, 费率为空", payment.getPaymentNo());
        }
        payment.setFeeAmount(dinNotifyDTO.getOrderFee());
        paymentMapper.updateByPrimaryKeySelective(payment);

        // 如果是组合支付，则更新子单
        if (Objects.equals(payment.getTradeType(), TradeTypeEnum.COMBINED_PAY.getDesc())) {
            List<PaymentCombinedDetail> details = paymentCombinedDetailService.selectByCombinedPaymentNo(paymentNo);
            PaymentCombinedDetail combinedDetail = details.stream().filter(el -> Objects.equals(el.getOnlinePayChannel(), OnlinePayChannelEnum.DIN_PAY.getChannel()))
                    .findFirst().orElseThrow(() -> new ProviderException("未查询到支付子单"));

            PaymentCombinedDetail update = getDinCombinedDetail(dinNotifyDTO, payment, combinedDetail);
            paymentCombinedDetailService.updateByPrimaryKeySelective(update);

            PaymentCombinedDetail nativeCombinedDetail = details.stream()
                    .filter(el -> Objects.isNull(el.getOnlinePayChannel()))
                    .findFirst()
                    .orElseThrow(() -> new ProviderException("未查询到本地支付子单"));
            paymentCombinedDetailService.decreaseFreezeAmount(nativeCombinedDetail);
        }
        return true;
    }

    private PaymentCombinedDetail getDinCombinedDetail(DinPayNotifyDTO dinNotifyDTO, Payment payment, PaymentCombinedDetail combinedDetail) {
        PaymentCombinedDetail update = new PaymentCombinedDetail();
        update.setId(combinedDetail.getId());
        update.setTransactionId(payment.getTransactionId());
        update.setTradeType(dinNotifyDTO.getPaymentType().concat("(").concat(dinNotifyDTO.getPaymentMethods()).concat(")"));
        update.setTradeState(payment.getTradeState());
        update.setTradeStateDesc(payment.getTradeStateDesc());
        update.setSpOpenid(payment.getSpOpenid());
        update.setBankType(payment.getBankType());
        update.setSuccessTime(payment.getSuccessTime());
        update.setFinishTime(payment.getFinishTime());
        update.setStatus(payment.getStatus());
        update.setFeeRate(payment.getFeeRate());
        update.setPaymentChannelId(payment.getPaymentChannelId());
        update.setFeeAmount(payment.getFeeAmount());
        return update;
    }

    @Override
    public Payment queryByNo(String paymentNo) {
        return paymentMapper.selectByPaymentNo(paymentNo);
    }
}
